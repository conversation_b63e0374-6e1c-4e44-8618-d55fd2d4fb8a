'use client';

import { Card, CardContent, CardHeader } from '@/components/ui/card';
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from '@/components/ui/collapsible';
import { ChevronDown, ChevronRight, Layers } from 'lucide-react';
import { Badge } from '@/components/ui/badge';
import { ProgramStepDetails } from './program-step-details';
import { FlowGroupRenderer } from './flow-group-renderer';
import { groupStepsByType, STEP_TYPE_CONFIG } from '@/utils/step-type-classifier';
import type { BodyPartGroup } from '@/utils/body-part-classifier';

interface BodyPartCardProps {
  group: BodyPartGroup | any; // any to handle subroutine groups
  globalIndex: number | string;
  isExpanded: boolean;
  onToggleExpansion: (index: number | string) => void;
  isSubroutine?: boolean;
  variant?: 'main' | 'nested'; // For styling differences
  expandedGroups?: Set<number | string>; // For subroutine internal expansion
}

export function BodyPartCard({
  group,
  globalIndex,
  isExpanded,
  onToggleExpansion,
  isSubroutine = false,
  variant = 'main',
  expandedGroups
}: BodyPartCardProps) {
  const isNested = variant === 'nested';
  
  // Styling based on variant and type
  const cardClassName = isNested
    ? "border-l-4 border-l-blue-200 hover:border-l-blue-300 transition-colors"
    : `border-l-4 ${isSubroutine ? 'border-l-violet-200 hover:border-l-violet-300' : 'border-l-blue-200 hover:border-l-blue-300'} transition-colors`;
  
  const headerClassName = isNested
    ? "cursor-pointer hover:bg-muted/30 transition-colors py-2"
    : "cursor-pointer hover:bg-muted/30 transition-colors py-2";
  
  const chevronSize = isNested ? "h-3 w-3" : "h-3 w-3";

  return (
    <Card key={globalIndex} className={cardClassName}>
      <Collapsible open={isExpanded} onOpenChange={() => onToggleExpansion(globalIndex)}>
        <CollapsibleTrigger asChild>
          <CardHeader className={headerClassName}>
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-2 flex-1">
                {isExpanded ? (
                  <ChevronDown className={`${chevronSize} text-muted-foreground`} />
                ) : (
                  <ChevronRight className={`${chevronSize} text-muted-foreground`} />
                )}
                <div className="flex-1">
                  {isSubroutine ? (
                    <>
                      <div className="text-sm font-medium flex items-center gap-2">
                        <Layers className="h-3 w-3 text-violet-600" />
                        {group.subroutineName || `Subroutine ${group.subroutineId}`}
                      </div>
                      <div className="text-xs text-muted-foreground mb-2">
                        Step {group.startStepNumber} • Subroutine {group.subroutineId} • {group.subroutineTotalSteps} internal steps
                      </div>
                    </>
                  ) : (
                    <>
                      <div className="text-sm font-medium">
                        {group.displayName}
                      </div>
                      <div className="text-xs text-muted-foreground mb-2">
                        Steps {group.startStepNumber}-{group.endStepNumber} • {group.stepCount} steps
                      </div>
                    </>
                  )}
                  {/* Technique Summary - only for non-subroutine groups */}
                  {!isSubroutine && (() => {
                    const stepTypeResult = groupStepsByType(group.steps);
                    const uniqueTechniques = Object.entries(stepTypeResult.techniqueDistribution)
                      .filter(([type, count]) => count > 0 && type !== 'unknown')
                      .sort(([,a], [,b]) => b - a)
                      .slice(0, 4); // Show top 4 techniques

                    return (
                      <div className="flex flex-wrap gap-1">
                        {uniqueTechniques.map(([type, count]) => {
                          const config = STEP_TYPE_CONFIG[type as keyof typeof STEP_TYPE_CONFIG];
                          return (
                            <Badge
                              key={type}
                              variant="secondary"
                              className="text-xs px-1.5 py-0.5 bg-indigo-50 text-indigo-700 border-indigo-200"
                              title={`${config.displayName}: ${count} steps`}
                            >
                              <span className="mr-0.5">{config.icon}</span>
                              {count}
                            </Badge>
                          );
                        })}
                        {stepTypeResult.techniqueDistribution.unknown > 0 && (
                          <Badge variant="outline" className="text-xs px-1.5 py-0.5 bg-amber-50 text-amber-700 border-amber-200">
                            ❓ {stepTypeResult.techniqueDistribution.unknown}
                          </Badge>
                        )}
                      </div>
                    );
                  })()}
                </div>
              </div>
            </div>
          </CardHeader>
        </CollapsibleTrigger>
        <CollapsibleContent>
          <CardContent className="pt-0 pb-2">
            <div className="border-t pt-2 overflow-hidden">
              {isSubroutine && expandedGroups ? (
                // Show subroutine's internal flow groups with same structure as main flow
                <div className="space-y-3">
                  <div className="text-xs text-muted-foreground mb-2">
                    Internal flow structure:
                  </div>
                  <FlowGroupRenderer
                    flowGroups={group.subroutineFlowGroups || []}
                    expandedGroups={expandedGroups}
                    onToggleExpansion={onToggleExpansion}
                    getGlobalIndex={(subGroup, flowIndex, groupIndex) =>
                      `${globalIndex}-${flowIndex}-${groupIndex}`
                    }
                    variant="nested"
                  />
                </div>
              ) : (
                // Show regular step details for body part groups
                <ProgramStepDetails group={group} />
              )}
            </div>
          </CardContent>
        </CollapsibleContent>
      </Collapsible>
    </Card>
  );
}
