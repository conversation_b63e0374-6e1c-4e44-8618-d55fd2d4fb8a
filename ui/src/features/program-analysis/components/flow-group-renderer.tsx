'use client';

import { Badge } from '@/components/ui/badge';
import { BodyPartCard } from './body-part-card';
import type { FlowGroup } from '@/utils/body-part-classifier';

interface FlowIndicatorProps {
  direction: FlowGroup['direction'];
  isFirst: boolean;
  isLast: boolean;
  variant?: 'main' | 'nested';
}

const FlowIndicator = ({ direction, isFirst: _isFirst, isLast: _isLast, variant = 'main' }: FlowIndicatorProps) => {
  if (direction === 'end' || direction === 'none') return null;

  const getDirectionConfig = () => {
    switch (direction) {
      case 'down':
        return {
          icon: '↓',
          color: 'text-teal-600',
          lineColor: 'border-teal-300'
        };
      case 'up':
        return {
          icon: '↑',
          color: 'text-amber-600',
          lineColor: 'border-amber-300'
        };
      case 'same':
        return {
          icon: '→',
          color: 'text-blue-600',
          lineColor: 'border-blue-300'
        };
      default:
        return {
          icon: null,
          color: 'text-muted-foreground',
          lineColor: 'border-muted'
        };
    }
  };

  const config = getDirectionConfig();
  const leftPosition = variant === 'nested' ? 'left-3' : 'left-3';

  return (
    <div className={`absolute ${leftPosition} top-0 bottom-0 flex flex-col items-center`}>
      {/* Vertical line spanning the entire flow group */}
      <div className={`w-0.5 flex-1 border-l-2 ${config.lineColor}`} />
      
      {/* Direction indicator positioned in the middle of the flow group */}
      {!_isLast && (
        <div className="absolute top-1/2 transform -translate-y-1/2 bg-background rounded-full p-1.5 border shadow-sm">
          <div className={config.color}>
            {config.icon}
          </div>
        </div>
      )}
    </div>
  );
};

interface FlowGroupRendererProps {
  flowGroups: FlowGroup[];
  expandedGroups: Set<number | string>;
  onToggleExpansion: (index: number | string) => void;
  getGlobalIndex: (group: any, flowIndex: number, groupIndex: number) => number | string;
  variant?: 'main' | 'nested';
  className?: string;
}

export function FlowGroupRenderer({
  flowGroups,
  expandedGroups,
  onToggleExpansion,
  getGlobalIndex,
  variant = 'main',
  className = ''
}: FlowGroupRendererProps) {
  const isNested = variant === 'nested';
  const containerPadding = isNested ? 'pl-6' : 'pl-8';
  const spacingClass = isNested ? 'space-y-1' : 'space-y-2';
  const headerMargin = isNested ? 'mb-2' : 'mb-3';
  const headerPadding = isNested ? 'pl-2' : 'pl-2';
  const dotSize = isNested ? 'w-1 h-1' : 'w-1.5 h-1.5';

  return (
    <div className={`space-y-4 ${className}`}>
      {flowGroups.map((flowGroup, flowIndex) => (
        <div key={flowIndex} className={`relative ${containerPadding}`}>
          {/* Flow indicator that spans the entire group */}
          <FlowIndicator
            direction={flowGroup.direction}
            isFirst={flowIndex === 0}
            isLast={flowIndex === flowGroups.length - 1}
            variant={variant}
          />
          
          {/* Flow group header */}
          {flowGroup.direction !== 'end' && (
            <div className={`${headerMargin} ${headerPadding}`}>
              <div className="flex items-center gap-2 text-xs">
                <div className={`${dotSize} rounded-full ${
                  flowGroup.direction === 'down' ? 'bg-teal-500' :
                  flowGroup.direction === 'up' ? 'bg-amber-500' : 'bg-blue-500'
                }`} />
                <span className={`font-medium ${
                  flowGroup.direction === 'down' ? 'text-teal-700' :
                  flowGroup.direction === 'up' ? 'text-amber-700' : 'text-blue-700'
                }`}>
                  {flowGroup.direction === 'down' ? 'Moving Down' :
                   flowGroup.direction === 'up' ? 'Moving Up' : 'Same Level'}
                </span>
                <Badge variant="outline" className="text-xs">
                  {flowGroup.bodyPartGroups.length} group{flowGroup.bodyPartGroups.length !== 1 ? 's' : ''}
                </Badge>
              </div>
            </div>
          )}
        
          <div className={spacingClass}>
            {flowGroup.bodyPartGroups.map((group, groupIndex) => {
              const globalIndex = getGlobalIndex(group, flowIndex, groupIndex);
              const isExpanded = expandedGroups.has(globalIndex);
              const isSubroutine = (group as any).bodyPart === 'subroutine';

              return (
                <BodyPartCard
                  key={globalIndex}
                  group={group}
                  globalIndex={globalIndex}
                  isExpanded={isExpanded}
                  onToggleExpansion={onToggleExpansion}
                  isSubroutine={isSubroutine}
                  variant={variant}
                  expandedGroups={expandedGroups}
                />
              );
            })}
          </div>
        </div>
      ))}
    </div>
  );
}
