'use client';

import { useState } from 'react';
import { Card, CardContent, CardHeader } from '@/components/ui/card';
import { Activity } from 'lucide-react';

import { FlowGroupRenderer } from './flow-group-renderer';
import type { ClassificationResult } from '@/utils/body-part-classifier';

interface ProgramFlowViewProps {
  classificationResult: ClassificationResult;
}


export function ProgramFlowView({ classificationResult }: ProgramFlowViewProps) {
  const [expandedGroups, setExpandedGroups] = useState<Set<number | string>>(new Set());

  const toggleGroupExpansion = (groupIndex: number | string) => {
    const newExpanded = new Set(expandedGroups);
    if (newExpanded.has(groupIndex)) {
      newExpanded.delete(groupIndex);
    } else {
      newExpanded.add(groupIndex);
    }
    setExpandedGroups(newExpanded);
  };

  const { flowGroups } = classificationResult;

  if (flowGroups.length === 0) {
    return (
      <Card>
        <CardContent className="flex items-center justify-center py-12">
          <div className="text-center">
            <Activity className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
            <h3 className="text-lg font-semibold mb-2">No Program Groups</h3>
            <p className="text-muted-foreground">
              No program groups could be identified from the program steps.
            </p>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader className="pb-3">
        <div className="flex items-center gap-2">
          <div className="p-1 bg-emerald-50 rounded border border-emerald-200">
            <Activity className="h-3 w-3 text-emerald-600" />
          </div>
          <div>
            <h3 className="text-sm font-medium">Program Flow</h3>
            <p className="text-xs text-muted-foreground">
              Visual timeline showing focus on different body parts
            </p>
          </div>
        </div>
      </CardHeader>
      <CardContent>

        <FlowGroupRenderer
          flowGroups={flowGroups}
          expandedGroups={expandedGroups}
          onToggleExpansion={toggleGroupExpansion}
          getGlobalIndex={(group, _flowIndex, _groupIndex) =>
            classificationResult.bodyPartGroups.indexOf(group)
          }
          variant="main"
        />
      </CardContent>
    </Card>
  );
}
